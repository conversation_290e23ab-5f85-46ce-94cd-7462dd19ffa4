"""
推荐问题API接口模块。
提供获取AI推荐问题的HTTP接口。
"""
import asyncio
from flask import request, jsonify, Blueprint, session

from src.services.agent.bots.user_query_recommendation_bot import UserQueryRecommendationBot
from src.services.auth.user_login_with_feishu import login_required
from src.services.chatbot.history_service import (
    get_user_latest_queries,
    get_other_users_latest_queries,
)
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email

# 创建推荐API的Blueprint
recommendation_bp = Blueprint('recommendation', __name__, url_prefix='/api')


@recommendation_bp.route('/recommendations', methods=['GET'])
@login_required
def get_recommendations():
    """
    GET /api/recommendations 接口，获取AI推荐的问题列表。
    
    查询参数:
        count (int, optional): 推荐问题数量，默认6个，最大10个
    
    返回:
        JSON格式的推荐问题列表
        {
            "success": true,
            "recommendations": ["问题1", "问题2", ...],
            "count": 实际返回的问题数量
        }
    """
    # 获取用户信息
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)

    # 获取推荐数量参数
    try:
        count = int(request.args.get('count', 6))
        # 限制推荐数量范围
        if count < 1:
            count = 1
        elif count > 10:
            count = 10
    except ValueError:
        return jsonify({
            "success": False,
            "error": "count参数必须是有效的整数"
        }), 400

    logger.info(f"用户 {username} ({email}) 请求 {count} 个推荐问题")

    try:
        # 1. 获取当前用户的历史消息
        current_user_messages = get_user_latest_queries(user_email=email, limit=10)
        logger.info(f"为用户 {email} 获取到 {len(current_user_messages)} 条历史消息")

        # 2. 获取其他用户的历史消息
        other_users_messages = get_other_users_latest_queries(current_user_email=email, limit=10)
        logger.info(f"为用户 {email} 获取到 {len(other_users_messages)} 条其他用户消息作为参考")

        # 3. 检查是否有足够的历史数据
        if not current_user_messages and not other_users_messages:
            logger.info(f"用户 {email} 没有任何历史消息，返回空推荐列表")
            return jsonify({
                "success": True,
                "recommendations": [],
                "count": 0,
                "message": "暂无历史数据，无法生成推荐问题"
            })

        # 4. 创建推荐机器人并获取推荐
        recommendation_bot = UserQueryRecommendationBot(user_info)
        
        # 使用asyncio运行异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            recommendations = loop.run_until_complete(
                recommendation_bot.get_recommendations(
                    current_user_messages=current_user_messages,
                    other_users_messages=other_users_messages
                )
            )
        finally:
            loop.close()

        # 5. 根据请求的数量截取推荐结果
        final_recommendations = recommendations[:count]
        
        logger.info(f"为用户 {email} 生成了 {len(final_recommendations)} 个推荐问题")

        return jsonify({
            "success": True,
            "recommendations": final_recommendations,
            "count": len(final_recommendations)
        })

    except Exception as e:
        logger.error(f"为用户 {email} 生成推荐问题时发生错误: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "生成推荐问题时发生内部错误，请稍后重试"
        }), 500
