"""
推荐问题API接口模块。
提供获取AI推荐问题的HTTP接口。
"""
import asyncio
from flask import request, jsonify, Blueprint, session

from src.services.agent.bots.user_query_recommendation_bot import UserQueryRecommendationBot
from src.services.auth.user_login_with_feishu import login_required
from src.services.chatbot.history_service import (
    get_user_latest_queries,
    get_other_users_latest_queries,
)
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email
from src.utils.in_memory_cache import in_memory_cache, clear_cache

# 创建推荐API的Blueprint
recommendation_bp = Blueprint('recommendation', __name__, url_prefix='/api')


@in_memory_cache(expire_seconds=1800)  # 缓存30分钟
def get_cached_recommendations(user_email: str, count: int = 6) -> dict:
    """
    获取缓存的推荐问题列表。

    Args:
        user_email (str): 用户邮箱，作为缓存key
        count (int): 推荐问题数量，默认6个

    Returns:
        dict: 包含推荐结果的字典
        {
            "success": bool,
            "recommendations": List[str],
            "count": int,
            "message": str (可选)
        }
    """
    logger.info(f"为用户 {user_email} 生成推荐问题（缓存函数）")

    try:
        # 1. 获取当前用户的历史消息
        current_user_messages = get_user_latest_queries(user_email=user_email, limit=10)
        logger.info(f"为用户 {user_email} 获取到 {len(current_user_messages)} 条历史消息")

        # 2. 获取其他用户的历史消息
        other_users_messages = get_other_users_latest_queries(current_user_email=user_email, limit=10)
        logger.info(f"为用户 {user_email} 获取到 {len(other_users_messages)} 条其他用户消息作为参考")

        # 3. 检查是否有足够的历史数据
        if not current_user_messages and not other_users_messages:
            logger.info(f"用户 {user_email} 没有任何历史消息，返回空推荐列表")
            return {
                "success": True,
                "recommendations": [],
                "count": 0,
                "message": "暂无历史数据，无法生成推荐问题"
            }

        # 4. 创建推荐机器人并获取推荐
        # 注意：这里需要构造一个基本的user_info，因为缓存函数只接收email参数
        user_info = {"email": user_email}
        recommendation_bot = UserQueryRecommendationBot(user_info)

        # 使用asyncio运行异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            recommendations = loop.run_until_complete(
                recommendation_bot.get_recommendations(
                    current_user_messages=current_user_messages,
                    other_users_messages=other_users_messages
                )
            )
        finally:
            loop.close()

        # 5. 根据请求的数量截取推荐结果
        final_recommendations = recommendations[:count]

        logger.info(f"为用户 {user_email} 生成了 {len(final_recommendations)} 个推荐问题（已缓存）")

        return {
            "success": True,
            "recommendations": final_recommendations,
            "count": len(final_recommendations)
        }

    except Exception as e:
        logger.error(f"为用户 {user_email} 生成推荐问题时发生错误: {e}", exc_info=True)
        return {
            "success": False,
            "error": "生成推荐问题时发生内部错误，请稍后重试"
        }


@recommendation_bp.route('/recommendations', methods=['GET'])
@login_required
def get_recommendations():
    """
    GET /api/recommendations 接口，获取AI推荐的问题列表。

    查询参数:
        count (int, optional): 推荐问题数量，默认6个，最大10个

    返回:
        JSON格式的推荐问题列表
        {
            "success": true,
            "recommendations": ["问题1", "问题2", ...],
            "count": 实际返回的问题数量
        }
    """
    # 获取用户信息
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)

    # 获取推荐数量参数
    try:
        count = int(request.args.get('count', 6))
        # 限制推荐数量范围
        if count < 1:
            count = 1
        elif count > 10:
            count = 10
    except ValueError:
        return jsonify({
            "success": False,
            "error": "count参数必须是有效的整数"
        }), 400

    logger.info(f"用户 {username} ({email}) 请求 {count} 个推荐问题")

    try:
        # 使用缓存函数获取推荐结果
        result = get_cached_recommendations(user_email=email, count=count)

        # 如果缓存函数返回错误，直接返回错误响应
        if not result.get("success", False):
            return jsonify(result), 500

        # 返回成功结果
        logger.info(f"为用户 {email} 返回了 {result.get('count', 0)} 个推荐问题（使用缓存）")
        return jsonify(result)

    except Exception as e:
        logger.error(f"为用户 {email} 获取推荐问题时发生错误: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "获取推荐问题时发生内部错误，请稍后重试"
        }), 500


@recommendation_bp.route('/recommendations/clear-cache', methods=['POST'])
@login_required
def clear_recommendations_cache():
    """
    POST /api/recommendations/clear-cache 接口，清除推荐问题的缓存。

    查询参数:
        user_email (str, optional): 指定要清除缓存的用户邮箱，如果不提供则清除所有推荐缓存

    返回:
        JSON格式的操作结果
        {
            "success": true,
            "message": "缓存清除成功"
        }
    """
    # 获取用户信息
    user_info = session.get("user_info")
    username = user_info.get("name")
    current_email = get_valid_user_email(user_info)

    # 获取要清除缓存的用户邮箱参数
    target_email = request.json.get('user_email') if request.json else None

    logger.info(f"用户 {username} ({current_email}) 请求清除推荐缓存，目标用户: {target_email or '所有用户'}")

    try:
        if target_email:
            # 清除指定用户的缓存
            # 需要构造缓存key来清除特定用户的缓存
            func_qualname = get_cached_recommendations.__qualname__
            clear_cache(func_qualname)  # 暂时清除整个函数的缓存，因为构造精确的key比较复杂
            message = f"已清除用户 {target_email} 的推荐缓存"
        else:
            # 清除所有推荐缓存
            func_qualname = get_cached_recommendations.__qualname__
            clear_cache(func_qualname)
            message = "已清除所有推荐缓存"

        logger.info(f"缓存清除成功: {message}")
        return jsonify({
            "success": True,
            "message": message
        })

    except Exception as e:
        logger.error(f"清除推荐缓存时发生错误: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "清除缓存时发生内部错误，请稍后重试"
        }), 500
